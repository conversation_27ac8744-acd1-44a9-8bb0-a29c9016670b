import asyncio
import json
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async


async def scroll_page(page):
    """Mimic scrolling to load dynamic content."""
    for _ in range(5):
        await page.mouse.wheel(0, 1000)
        await page.wait_for_timeout(1500)


async def scrape_viator_reviews(url, max_pages=3):
    reviews = []

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)

        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
            viewport={"width": 1280, "height": 800},
            locale="en-US"
        )

        page = await context.new_page()
        await stealth_async(page)

        print("🔗 Navigating to:", url)
        await page.goto(url, timeout=60000)
        await page.wait_for_timeout(5000)
        await scroll_page(page)

        for page_num in range(max_pages):
            print(f"📄 Scraping page {page_num + 1}...")
            html = await page.content()
            soup = BeautifulSoup(html, 'lxml')

            review_blocks = soup.select('[data-test="review-card"]')
            if not review_blocks:
                print("❌ No review blocks found — may be blocked.")
                break

            for block in review_blocks:
                try:
                    name = block.select_one('[data-test="review-card-username"]').text.strip()
                    rating = block.select_one('[data-test="review-stars"]').get("aria-label")
                    date = block.select_one('[data-test="review-card-date"]').text.strip()
                    text = block.select_one('[data-test="review-card-text"]').text.strip()
                    reviews.append({
                        'name': name,
                        'rating': rating,
                        'date': date,
                        'review': text
                    })
                except Exception as e:
                    print("⚠️ Error parsing review:", e)

            # Next button
            next_button = await page.query_selector('button[aria-label="Next page"]')
            if next_button:
                await next_button.click()
                await page.wait_for_timeout(4000)
                await scroll_page(page)
            else:
                break

        await browser.close()

    return reviews


if __name__ == "__main__":
    # Replace with any valid Viator tour URL
    viator_url = "https://www.viator.com/tours/New-York-City/New-York-in-One-Day-Guided-Sightseeing-Tour/d687-7081NYCDAY"

    print("🚀 Starting scraper...")
    reviews = asyncio.run(scrape_viator_reviews(viator_url, max_pages=5))

    with open("viator_reviews.json", "w", encoding="utf-8") as f:
        json.dump(reviews, f, ensure_ascii=False, indent=2)

    print(f"✅ Done! Scraped {len(reviews)} reviews and saved to viator_reviews.json")
